module Pages
  module Components
    class Cols4VariantsV1 < Cols4V1
      include VariantComponent

      ALLOWED_KEYS = [
        :view_more_url,
        items: [:variant_id, :variant_product_sku]
      ].freeze

      N_OF_ITEMS = 8

      attr_accessor :store

      def featured_variants(store)
        @store = store
        @featured_variants ||= Mkp::Variant.includes(variant_includes).where('mkp_variants.id IN (?)', featured_variants_ids)

        # NO duplicar elementos - devolver solo los configurados
        # La lógica anterior duplicaba variantes para completar N_OF_ITEMS
        # Esto causaba que 1 producto configurado apareciera 8 veces
        return @featured_variants
      end

      # Método para mantener el comportamiento anterior si es necesario
      def featured_variants_with_duplication(store)
        @store = store
        @featured_variants_with_duplication ||= Mkp::Variant.includes(variant_includes).where('mkp_variants.id IN (?)', featured_variants_ids)

        while (@featured_variants_with_duplication.size < N_OF_ITEMS && @featured_variants_with_duplication.size > 0)
          @featured_variants_with_duplication.each do |var|
            @featured_variants_with_duplication.push(var)
            if @featured_variants_with_duplication.size == N_OF_ITEMS
              return @featured_variants_with_duplication
            end
          end
        end
        return @featured_variants_with_duplication
      end

      def init
        if super
          self.columns = 4
          N_OF_ITEMS.times do |index|
            self.items << {
              variant_id: nil,
              variant_product_sku: nil
            }
          end
          self.setup[:items] = items
        end
      end

      private

      def variant_includes
        [:picture, product: [:shop, :manufacturer, :category]]
      end
    end
  end
end
