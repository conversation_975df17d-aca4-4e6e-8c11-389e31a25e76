module Pioneer
  class BinesController < Pioneer::ApplicationController

    before_filter :find_or_initialize, only: [:new, :edit, :update, :destroy]
    before_filter :build_installments, only: [:new, :edit]
    before_filter :initialize_categories, only: [:new, :edit]
    before_filter :check_store_owner, only: [:edit, :destroy]
    before_filter :have_permissions_to_read, only: [:index, :create, :new]
    before_filter :have_permissions_to_write, only: [:create, :new, :update, :destroy]
    before_filter :validate_store_id_param, only: [:create, :update]
    before_filter :get_associated_shops, only: [:edit]
    layout 'bootstrap_layout'

    def index
      if current_user.has_role?(:administrator)
        stores = Mkp::Store.all
        @bines = Bin.includes(:installments, :bank, :store).where(network: @network)
      else
        stores = current_user.role.store
        store = stores
        @bines = Bin.where(network: @network).where(store_id: current_user.role.store.id)
      end
      if params[:store_id].present?
        @bines = @bines.where(store_id: params[:store_id])
      end
      @bines = @bines.modo if modo?
      @bines = @bines.order(created_at: :desc).paginate(page: params[:page], per_page: 50)
    end

    def create
      bines = Bin.create(bines_attributes)
      if bines.any?{|bin| bin.errors.any?}
        notice = bines.map do |bin|
          "Numero:#{bin.number} #{bin.errors.full_messages.join(',')}" unless bin.valid?
        end.compact
        flash[:notice] = notice.join('<br>').html_safe
      end
      redirect_to params["bin"]["brand"] == 'modo' ? bines_url(brand: 'modo') : bines_url
    end

    def destroy
      @bin.destroy
      redirect_to bines_url
    end

    def update
      if @bin.update_attributes(bin_params)
        flash[:success] = t('pioneer.bines.updated')
        redirect_to bines_url
      else
        flash[:error] = 'Fail'
        render 'edit'
      end
    end

    private

    def bin_params
      params.require(:bin)
            .permit(:starts_at, :ends_at, :bank_id, :brand, :store_id, :number,
              installments_attributes: [
                :id, :number, :cft, :tea, :tna, :cftna, :tna_coef, :coef, :number, :search, :category_id, :product_id, :purchase_total_limit, :valid_for, :gateway, :_destroy
              ])
    end

    def find_or_initialize
      @bin = Bin.find_or_initialize_by(id: params[:id])
      @bin.brand = 'modo' if modo?
    end

    def initialize_categories
      @categories = (Mkp::Category.all.collect { |each| [each.full_path_name, each.id]}).sort_by(&:first)
    end

    def build_installments
      @bin.installments.present? && return
    end

    def bines_attributes
      attribute = bin_params.dup
      numbers = attribute.delete(:number).split(',')
      numbers.map{ |number| attribute.merge(number: number, network: @network) }
    end

    def check_store_owner
      redirect_to bines_path unless current_admin.is_store_owner?(@bin.store)
    end

    def validate_store_id_param
      return if bin_params[:store_id].nil?
      store = current_user.stores.find_by_id(bin_params[:store_id])
      redirect_to bines_path unless current_admin.is_store_owner?(store)
    end

    def have_permissions_to_read
      redirect_to home_url, :alert => "Have not permissions" if cannot?(:read, "Bines")
    end
    def have_permissions_to_write
      redirect_to bines_url, :alert => "Have not permissions" if cannot?(:crud, "Bines")
    end

    def modo?
      params[:brand] == 'modo'
    end

    def get_associated_shops
      @associated_shops = Mkp::ShopStore.where(bin: @bin).includes(:shop)
    end
  end
end
