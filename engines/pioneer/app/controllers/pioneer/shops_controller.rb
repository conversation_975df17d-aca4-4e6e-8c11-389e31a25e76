module Pioneer
  class ShopsController < Pioneer::ApplicationController
    #load_and_authorize_resource
    include PioneerIntegrationsLoaders
    include IntegrationsHelper
    layout 'bootstrap_layout'
    before_filter :have_permissions_to_read, :only => [:index, :show, :new, :edit, :create]
    before_filter :have_permissions_to_write, :except => [:index]
    before_filter :find_shop, only: [:edit, :update, :destroy]
    before_filter :get_stores
    before_filter :modo_bines, only: [:new, :edit]
    INTEGRATION_NAME = Network[@network].mercadolibre_integration_name.freeze

    before_action :load_shops, only: :index

    def index
      @shops = @shops.order(title: :asc).paginate(page: params[:page])
    end

    def show
      @shop = Shop.by_network(@network).friendly.find(params[:id])
    end

    def new
      @shop = Mkp::Shop.new
      @shop.build_setting
      @shop.first_data_credentials.build
      load_available_owners
    end

    def edit
      load_available_owners
      @shop.first_data_credentials.build if @shop.first_data_credentials.blank?
      render :new
    end

    def create
      @shop = Mkp::Shop.new(shop_params)
      @shop.visible = false
      @shop.network = 'AR'

      if @shop.save
        fulfillment_shop_id = params[:fulfilled_by_gp] == '1' ? 1 : @shop.id
        @shop.update_attribute(:fulfillment_shop_id, fulfillment_shop_id)
        @shop.assign_owner(owner_params[:owner][:id])
        @shop.create_setting(setting_params)
        save_shop_stores

        redirect_to shops_url, flash: { success: t('pioneer.shop.new-success') }
      else
        flash[:error] = t('pioneer.shop.new-error')
        render :new
      end
    end

    def update
      ActiveRecord::Base.transaction do
        if @shop.update(shop_params)
          fulfillment_shop_id = params[:fulfilled_by_gp] == '1' ? 1 : @shop.id
          @shop.update_attribute(:fulfillment_shop_id, fulfillment_shop_id) unless @shop.fulfillment_shop_id == fulfillment_shop_id
          if current_user.has_role?(:administrator) && owner_params.dig(:owner, :id).present?
            @shop.assign_owner(owner_params[:owner][:id]) unless @shop.owner.id == owner_params[:owner][:id]
          end
          update_settings
          save_shop_stores if store_relationships_changed?

          redirect_to edit_shop_url(@shop), flash: { success: t('pioneer.shop.update-success') }
        else
          flash[:error] = @shop.errors.full_messages.to_sentence
          redirect_to edit_shop_url(@shop)
          raise ActiveRecord::Rollback
        end
      end
    end

    def destroy
      @shop = Mkp::Shop.friendly.find(params[:id])
      @shop.destroy
      redirect_to :back, flash: { success: "The #{@shop.title} Shop was destroy successfully" }
    end

    def integrate_with_meli
      @shop = Mkp::Shop.find(params[:id])
      @products = Mkp::Product.where(shop_id: @shop.id)

      if session[:errors].present?
        @errors = session.delete(:errors)
      end

      @integrated_products, @non_integrated_products = @products.partition do |product|
        product.external_objects.where(integration_name: 'gpmercadolibre').present? || \
          product.variants.select{|v| !v.external_objects.empty?}.present?
      end

      render 'gp_meli_index'
    end

    def create_products_meli
      selected_products_id = params[:products].map do |product_params|
        product_params[:id] if product_params[:integrate] == "true"
      end.compact
      selected_products = Mkp::Product.where(id: selected_products_id)

      # NOTE: Maybe we could extract to a before_filter.
      shop = Mkp::Shop.find(Network[@network].default_shop_id)
      integration_name = Network[@network].pioneer_integrations.first

      unless (integration = load_authorized_integrations(integration_name)).present?
        integration = get_integration(shop, integration_name)
        integration.save_authorization
      end

      # NOTE: Candidate for refactoring.
      api_errors = selected_products.each_with_object([]) do |product, errors|
        result = integration.create_meli_item(product, @network.downcase)
        errors << result if result.is_a?(Hash)
      end

      errors = formatted_errors_for(selected_products)

      flash[:api_errors] = api_errors if api_errors.present?
      session[:errors] = errors if errors.present?

      redirect_to integrate_with_meli_shop_path
    end

    # NOTE: Create/Delete on the integration side should be equally implemented!
    def delete_products_meli
      shop_id = Network[@network].default_shop_id
      shop = Mkp::Shop.find(shop_id)

      selected_products = params[:products].select { |p| p[:integrate] == "true"}
      integration = get_integration(shop, Network[@network].pioneer_integrations.first)

      selected_products.each do |product_id|
        integration.delete_meli_item(product_id['id'])
      end

      redirect_to integrate_with_meli_shop_path
    end

    def import
      full_path = params[:sellers][:file].tempfile
      Import::ShopsPrograms.new(full_path).call
      redirect_to shops_url
    end

    def export # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
      schedule_job = Sidekiq::ScheduledSet.new.size
      time_to_run = schedule_job.positive? ? schedule_job * 4 : 4
      shops_ids = Mkp::Shop.all.map(&:id)

      if shops_ids.any?
        export = current_user.role.store.exports.shops.create
        ExportShopsWorker.perform_in(time_to_run, shops_ids, export.id)
        flash[:success] = "Tu export estará disponible a
                           las: #{(Time.zone.now + time_to_run.minutes).strftime('%H:%M')}
                           aproximadamente, el link para descargar el export aparecerá en el
                           #{view_context.link_to('listado de exports', exports_path(export_type: 'shops'))}.".html_safe

      else
        flash[:error] = 'No se han encontrado proveedores, por favor intentá de nuevo.'
      end
      redirect_to shops_path
    end

    private

    def shop_params
      params.require(:mkp_shop).permit(
        :title, :decidir_site_id, :decidir_percentage, :decidir_public_key, :decidir_private_key, :fc,
        :delivery_by_matrix, :reminder, :shipment_strategy, :mercadolibre_app_id, :mercadolibre_secret_key, :allows_pickup,
        :order_visibility, :cuit, :external_shop_id, :cucarda_id, :cucarda_active, :account_number,
        first_data_credentials_attributes: [
          :id, :store_id, :firstdata_store, :user, :password, :certificate_password, :ssl_cert, :ssl_cert_key, :priority, :category, :_destroy
        ],
        payment_credentials_attributes: %i[id name value _destroy store_id]
      )
    end

    def setting_params
      params.require(:setting).permit(
        :company_name, :account_executive,
        business_number: [:visa, :cabal, :mastercard, :american, :naranja],
        contact_email: [:comercial, :logistica, :liquidaciones],
        contact_number: [:comercial, :logistica, :liquidaciones],
        commercial_agreement: [:monthly_fee, :sale_commission]
      )
    end

    def owner_params
      params.require(:mkp_shop).permit(owner: [:id])
    end

    def stores_params
      params.require(:mkp_shop).permit(shop_stores_attributes: [:store_id])
    end

    def find_shop
      @shop = Mkp::Shop.includes(admins: { profile: :avatar }).friendly.find(params[:id])
    end

    def load_available_owners
      @users = User.joins("LEFT JOIN mkp_shop_admins ON users.id = mkp_shop_admins.admin_id")
                   .includes(profile: :avatar)
                   .where(mkp_shop_admins: {admin_id: nil})
                   .limit(100)
                   .order('users.created_at desc')
      if @shop.persisted?
        @users = [@shop.owner] + @users if @shop.owner.present?
      end
    end

    def load_shops
      @shops = Shop.by_network(@network).includes(:setting).order(title: :asc)

      if params[:query].present?
        @shops = @shops.where('title LIKE ? OR slug LIKE ?', "%#{params[:query]}%", "%#{params[:query]}%")
      end

      if params[:shop_visibility].present?
        @shop_visibility = params[:shop_visibility].to_i
        @shops = @shops.where(visible: @shop_visibility)
      end

      if params[:fulfilled].present? && @network == 'AR'
        @fulfilled = params[:fulfilled].to_i
        default_fulfillment_shop_id = Network[@network].default_shop_id

        @shops = if @fulfilled == 1
          @shops.where(fulfillment_shop_id: default_fulfillment_shop_id)
        else
          @shops.where('fulfillment_shop_id != ?', default_fulfillment_shop_id)
        end
      end

      unless current_user.has_role?(:administrator)
        @shops = @shops.includes(:stores).where(id: current_user.role.store.shops.collect(&:id))
      end
    end

    def save_shop_stores
      if current_user.has_role?(:administrator)
        return if stores_params.blank?

        store_ids_to_keep = []
        shop_store_updates = {}

        params[:mkp_shop][:shop_stores_attributes].each do |_, attrs|
          next if attrs['_destroy'] != 'false'
          store_id = attrs[:store_id].to_i
          store_ids_to_keep << store_id

          shop_store_updates[store_id] = {
            commission: attrs[:commission].blank? ? nil : attrs[:commission].to_d,
            payment_program_id: attrs[:payment_program_id].blank? ? nil : attrs[:payment_program_id].to_d,
            bin_id: attrs[:bin_id].blank? ? nil : attrs[:bin_id].to_d,
            start_date: attrs["start_date"],
            end_date: attrs["end_date"],
            active: attrs["active"]
          }.compact
        end

        @shop.shop_stores.where.not(store_id: store_ids_to_keep).destroy_all

        store_ids_to_keep.each do |store_id|
          shop_store = @shop.shop_stores.find_or_initialize_by(store_id: store_id)
          shop_store.update(shop_store_updates[store_id])
        end
      else
        store_id = current_user.role.store.id

        if stores_params.blank?
          @shop.shop_stores.where(store_id: store_id).destroy_all
        else
          store_params = nil
          params[:mkp_shop][:shop_stores_attributes].each do |_, attrs|
            if attrs[:store_id].to_i == store_id && attrs['_destroy'] == 'false'
              store_params = attrs
              break
            end
          end

          if store_params
            shop_store = @shop.shop_stores.find_or_initialize_by(store_id: store_id)

            attributes_to_update = {}
            attributes_to_update[:commission] = store_params[:commission] unless store_params[:commission].blank?
            attributes_to_update[:payment_program_id] = store_params[:payment_program_id].to_d unless store_params[:payment_program_id].blank?
            attributes_to_update[:bin_id] = store_params[:bin_id].to_d unless store_params[:bin_id].blank?

            shop_store.update(attributes_to_update)
          else
            @shop.shop_stores.where(store_id: store_id).destroy_all
          end
        end
      end

      @shop.update_bna_offices if needs_bna_offices_update?
    end

    def update_settings
      @shop.setting.update_attributes(setting_params)
    end

    def have_permissions_to_read
      redirect_to home_url, :alert => "Have not permissions" if cannot?(:read, "Shop")
    end

    def have_permissions_to_write
      redirect_to home_url, :alert => "Have not permissions" if cannot?(:crud, "Shop")
    end

    def get_stores
      @stores = current_user.has_role?(:administrator) ? Mkp::Store.all : [current_user.role.store]
    end

    def modo_bines
      @modo_bines ||= Bin.modo
    end

    def store_relationships_changed?
      return false unless params[:mkp_shop]&.key?('shop_stores_attributes')

      params[:mkp_shop][:shop_stores_attributes].any? do |_, attrs|
        return true if attrs['id'].blank? || attrs['_destroy'] == 'true'

        existing = @shop.shop_stores.find_by(id: attrs['id'])
        next false unless existing

        field_comparisons = {
          'store_id' => { new: attrs['store_id']&.to_i, current: existing.store_id },
          'commission' => { new: attrs['commission'].present? ? attrs['commission'].to_f : nil, current: existing.commission&.to_f },
          'payment_program_id' => { new: attrs['payment_program_id'].present? ? attrs['payment_program_id'].to_i : nil, current: existing.payment_program_id },
          'bin_id' => { new: attrs['bin_id'].present? ? attrs['bin_id'].to_i : nil, current: existing.bin_id },
          'start_date' => { new: attrs['start_date'].presence, current: existing.start_date&.strftime('%Y-%m-%dT%H:%M') },
          'end_date' => { new: attrs['end_date'].presence, current: existing.end_date&.strftime('%Y-%m-%dT%H:%M') },
          'active' => { new: attrs['active'] == 'true', current: existing.active }
        }

        field_comparisons.any? { |_, comparison| comparison[:new] != comparison[:current] }
      end
    end

    def needs_bna_offices_update?
      @shop.stores.map(&:id).include?(Mkp::Shop::BNA_MIMOTO_ID)
    end
  end
end
