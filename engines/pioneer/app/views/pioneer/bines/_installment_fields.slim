.nested-fields
  .col-lg-12
    .panel.panel-default
      .panel-body
        .row
          .col-lg-2
            = f.label :tea, t('pioneer.bines.installments')
          .col-lg-2
            = f.number_field :number, class: 'form-control', step: :any, label: false
          .col-lg-2
            = f.label :purchase_total_limit, t('pioneer.bines.purchase-total-limit')
          .col-lg-2
            = f.number_field :purchase_total_limit, class: 'form-control', label: false
          .col-lg-2
            = f.label :valid_for, t('pioneer.bines.valid-for')
          .col-lg-2
            = f.select :valid_for, options_for_select([['Primera Compra', 'first_purchase'], ['Usuario en whitelist', 'whitelist_user']], f.object.valid_for), {include_blank: true, label: false}, {class: 'form-control'}
        br
        .row
          .col-lg-2
            = f.label :tea, t('pioneer.bines.tea')
          .col-lg-2
            = f.number_field :tea, class: 'form-control', step: :any, label: false
          .col-lg-2
            = f.label :cft, t('pioneer.bines.cft')
          .col-lg-2
            = f.number_field :cft, class: 'form-control', step: :any, label: false
          .col-lg-2
            = f.label :coef, t('pioneer.bines.coef')
          .col-lg-2
            = f.number_field :coef, class: 'form-control', step: :any, label: false
        br
        .row
          .col-lg-2
            = f.label :cftna, t('pioneer.bines.cftna')
          .col-lg-2
            = f.text_field :cftna, placeholder: '', class: 'form-control', label: false
          .col-lg-2
            = f.label :tna, t('pioneer.bines.tna')
          .col-lg-2
            = f.text_field :tna, placeholder: '', class: 'form-control', label: false
          .col-lg-2
            = f.label :coef, t('pioneer.bines.coef')
          .col-lg-2
            = f.number_field :tna_coef, class: 'form-control', step: :any, label: false

        br
        .row
          .col-lg-2
            = f.label t('pioneer.bines.search-category')
          .col-lg-2
            = f.text_field "search", onkeyup: "filter(this.id)", label: false, class: 'form-control'
          .col-lg-2
            = f.label t('pioneer.bines.category')
          .col-lg-6
            = f.select :category_id, options_for_select(@categories, f.object.category_id), {include_blank: true, label: false}, {class: 'form-control', onChange: "filter_products(this.id)"}
        br
        .row
          .col-lg-2
            = f.label t('pioneer.bines.product')
          .col-lg-6
            = f.select :product_id, options_for_select([f.object.product ? [f.object.product.title, f.object.product_id] : []], f.object.product_id), {label: false}, {class: 'form-control'}
        br
          .row
            .col-lg-2
              = link_to_remove_association t('pioneer.bines.installments-remove'), f