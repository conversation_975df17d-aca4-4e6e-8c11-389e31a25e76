nav.navbar.navbar-inverse
  .container-fluid
    .navbar-header
      button.navbar-toggle.collapsed aria-expanded="false" data-target="#bs-example-navbar-collapse-1" data-toggle="collapse" type="button"
        span.sr-only Toggle navigation
        span.icon-bar
        span.icon-bar
        span.icon-bar
      a.navbar-brand  Pioneer
    #bs-example-navbar-collapse-1.collapse.navbar-collapse
      ul.nav.navbar-nav
        - if can?(:read, 'Marketplace' )
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.marketplace')
              span.caret
            ul.dropdown-menu
              - if can?(:read, 'Shop')
                  li
                    a.dropdown-item  href="#{shops_url}" = t('pioneer.menu.shop')
              - if can?(:read, 'Integration')
                  li
                    a.dropdown-item href="#{integrations_shops_url}" = t('pioneer.menu.integrations')
              - if can?(:read, 'Store')
                  li
                    a.dropdown-item href="#{stores_url}" = t('pioneer.menu.stores')
              - if can?(:read, "Category")
                  li
                    a.dropdown-item href="#{categories_url}" = t('pioneer.menu.categories')
              - if can?(:read, "Variant_Cost")
                  li
                    a.dropdown-item href="#{costs_url}" = t('pioneer.menu.variant-costs')
              - if can?(:read, "Product")
                  li
                    a.dropdown-item href="#{products_url}" = t('pioneer.menu.products')
              - if can?(:read, "Vouchers")
                  li
                    a.dropdown-item href="#{vouchers_url}" = t('pioneer.menu.vouchers')
                  li.divider
              - if can?(:read, 'Order')
                  li
                    a.dropdown-item href="#{sales_url}" = t('pioneer.menu.orders')
                  li.divider
              - if can?(:read, 'dashboard')
                  li
                    a.dropdown-item href="#{dashboard_index_path}" = t('pioneer.menu.Dashboard')
                  li.divider
              - if can?(:read, 'Users_Points')
                  li
                    a.dropdown-item href="#{users_points_url}" = t('pioneer.menu.user-points')
              - if can?(:read, 'Products_Points')
                  li
                    a.dropdown-item href="#{products_stores_url}" = t('pioneer.menu.product-points')

              - if can?(:read, 'Abandoned_Carts')
                  li
                    a.dropdown-item href="#{carts_url}" = t('pioneer.menu.abandoned-carts')
                  /= link_to 'Fulfillment', fulfillment_orders_url,class:"dropdown-item"
                  /= link_to 'Reviews' ,reviews_url,class:"dropdown-item"
                  /= link_to 'Questions',questions_url,class:"dropdown-item"
                  /= link_to 'Suggestions', suggestions_url,class:"dropdown-item"

        - if can?(:read, 'Menu' )
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.menu')
              span.caret
            ul.dropdown-menu
              - if can?(:read, 'Sort')
                li
                  a.dropdown-item href="#{menu_index_path}" = t('pioneer.menu.sort')
              - if can?(:read, 'Manage')
                li
                  a.dropdown-item href="#{customized_menu_index_path}" = t('pioneer.menu.manage')

        - if can?(:read, 'Marketing' )
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.marketing')
              span.caret
            ul.dropdown-menu
              - if can?(:read, "Cucardas")
                li
                  a.dropdown-item href="#{cucardas_url}" = t('pioneer.menu.cucardas')
              - if can?(:read, 'Coupon' )
                  li
                    a.dropdown-item href="#{coupons_path}" = t('pioneer.menu.coupons')
              - if can?(:read, 'Landing' )
                li
                  a.dropdown-item href="#{landings_path}" = t('pioneer.menu.landings')
              - if can?(:read, 'Promotion' )
                  li
                    a.dropdown-item href="#{promotions_path}" = t('pioneer.menu.promotions')
              - if can?(:read, "LoyaltyConfigs")
                li
                  a.dropdown-item href="#{loyalty_configs_url}" = t('pioneer.menu.loyalty_configs')

        - if can?(:read, 'third_party_codes')
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.third_party')
              span.caret
            ul.dropdown-menu
              li
                a.dropdown-item href="#{third_party_codes_url}" = t('pioneer.menu.third_party_codes')
        li
          a.dropdown-item href="#{blacklists_url}" = t('pioneer.menu.blacklists')
        - if can?(:read, 'Users' )
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.users')
              span.caret
            ul.dropdown-menu
              - if can?(:read, 'Shop_Users')
                li
                  a.dropdown-item href="#{social_users_url}" = t('pioneer.menu.shop_users')
              - if can?(:read, 'Guest')
                  //li= link_to 'Guest users', guests_url,  class: "dropdown-item"
              - if can?(:read, 'Customer')
                li
                  a.dropdown-item href="#{customers_url}" = t('pioneer.menu.customers')
              - if can?(:read, 'Brands')
                li
                  a.dropdown-item href="#{brands_url}" = t('pioneer.menu.brands')
              - if can?(:read, 'Dnis')
                li
                  a.dropdown-item href="#{dnis_url}" = t('pioneer.menu.dnis')
                li
                  a.dropdown-item href="#{bna_offices_url}" = t('pioneer.menu.offices')
              li.divider
              - if can?(:read, 'Admin')
                  li
                    a.dropdown-item href="#{admin_users_url}" = t('pioneer.menu.users-pioneer')
              - if can?(:read, 'Role')
                  li
                    a.dropdown-item href="#{roles_url}" = t('pioneer.menu.roles')

        - if can?(:read, 'Payments' )
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.payment')
              span.caret
            ul.dropdown-menu
              - if can?(:read, 'Bank')
                li
                  a.dropdown-item href="#{banks_path}" = t('pioneer.menu.banks')
              - if can?(:read, 'Bines')
                li
                  a.dropdown-item href="#{bines_path}" = t('pioneer.menu.bines')
              - if can?(:read, 'Admin')
                li
                  a.dropdown-item href="#{payment_programs_path}" = t('pioneer.menu.programs')
                li
                  a.dropdown-item href="#{bines_path(brand: 'modo')}" = t('pioneer.menu.modo')

        - if can?(:read, 'Accounting' )
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.accounting')
              span.caret
            ul.dropdown-menu
              - if can?(:read, 'Invoices' )
                li
                  a.dropdown-item href="#{accounting_invoices_path}" = t('pioneer.menu.invoices')
              - if can?(:read, 'Report' )
                li
                  a.dropdown-item href="#{accounting_reports_path}" = t('pioneer.menu.reports')

        - if can?(:read, 'Reports' )
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.reports')
              span.caret
            ul.dropdown-menu
              - if can?(:read, 'Cancelled_Orders' )
                li
                  a.dropdown-item href="#{reports_overview_path}" = t('pioneer.menu.cancelled')
                //li
                //  a.dropdown-item href="#{url_generator_index_path}" = t('pioneer.menu.url_generator')

        - if can?(:read, 'Tdd' ) && current_user.is_store_owner?(Mkp::Store.find(14))
            li.dropdown
              a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
                = t('pioneer.menu.tdd')
                span.caret
              ul.dropdown-menu
                - if can?(:read, 'Members' )
                  li
                    a.dropdown-item href="#{tdd_members_path}" = t('pioneer.menu.members')
                - if can?(:read, 'Payment' )
                  li
                    a.dropdown-item href="#{tdd_payments_path}" = t('pioneer.menu.payments')
                - if can?(:read, 'Planes' )
                  li
                    a.dropdown-item href="#{tdd_services_path}" = t('pioneer.menu.plans')
                li.divider
                - if can?(:read, 'Exports' )
                  li
                    a.dropdown-item href="#{tdd_exports_path}" = t('pioneer.menu.exports')
                - if can?(:read, 'Imports' )
                  li
                    a.dropdown-item href="#{tdd_imports_path}" = t('pioneer.menu.imports')

        - if can?(:read, 'invoice_management')
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.invoice_management')
              span.caret
            ul.dropdown-menu
              - if can?(:read, 'invoice_entities')
                li
                  a.dropdown-item href="#{dashboard_invoice_items_path}" = t('pioneer.menu.Dashboard')
                li
                  a.dropdown-item href="#{invoice_items_path}" = t('pioneer.menu.invoice_items')
                li
                  a.dropdown-item href="#{supplier_invoices_path}" = t('pioneer.menu.supplier_invoices')
                li
                  a.dropdown-item href="#{invoice_reports_path}" = t('pioneer.menu.invoice_reports')
              - if can?(:read, 'invoice_stocks')
                li.divider
                li
                  a.dropdown-item href="#{suppliers_path}" = t('pioneer.menu.suppliers')
                li
                  a.dropdown-item href="#{supplier_stocks_path}" = t('pioneer.menu.stocks')
        = render partial: "pioneer/partials/mimoto_menu"
        = render partial: "pioneer/partials/renaper_menu"

        - if Rails.env.development?
          li.dropdown
            a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
              = t('pioneer.menu.dev-tools')
              span.caret
            ul.dropdown-menu
              li
                  a.dropdown-item href="#{mail_preview_path}" = t('pioneer.menu.mails')

      ul.nav.navbar-nav.navbar-right
        li.dropdown
          a.dropdown-toggle aria-expanded="false" aria-haspopup="true" data-toggle="dropdown" href="#" role="button"
            = current_user.email
            span.caret
          ul.dropdown-menu
            li
              = link_to 'Logout', logout_path, class: "dropdown-item"
