.nested-fields#StoresBox
  .col-lg-12
    .panel.panel-default
      .panel-body
        .row
          .col-md-4
            .form-group
              = f.select :store_id, options_for_select(@stores.sort_by(&:name).map {|m| [m.name, m.id]}, f.object.store_id),{allow_blank: true}, {class: 'arguments form-control', style: 'width:100%'}
          .col-md-4
            .form-group
              = f.text_field :commission,
                pattern: '\d+(\.\d+)?',
                title: 'Is the monthly fee of the shop for this store',
                label: t('pioneer.shop.new-sale-commision'),
                allow_blank: true,
                class: 'form-control'
          .col-md-4
            .form-group
              = f.select :payment_program_id, options_for_select(PaymentProgram.all.map {|s| [s.name.capitalize, s.id.to_s]}, f.object.payment_program_id), {include_blank: true, label: "#{t('pioneer.shop.payment-program')}"}, class: "form-control required"
          .col-md-4
            .form-group
              = f.select :bin_id, options_for_select(@modo_bines.map { |s| [s.number, s.id.to_s]}, f.object.bin_id), { include_blank: true, label: "Modo cuotas" }, class: "form-control"
        .row#StartDate
          .col-md-3
            = f.label "Fecha de inicio: "
            = f.datetime_local_field :start_date, {min: Date.today, label: false, id: 'startDatePlace', class: 'form-control'}
            i#startTrashButton.fa.fa-trash.deleteDateStart data-toggle="tooltip" title="Eliminar fecha"
          .col-md-3
            = f.label "Fecha de finalización: "
            = f.datetime_local_field :end_date, {min: Date.today, label: false, id: 'endDatePlace', class: 'form-control'}
            i#endTrashButton.fa.fa-trash.deleteDateStart data-toggle="tooltip" title="Eliminar fecha"
          .col-md-1.mt-5
            .input-group
              span.input-group-addon = t('Activar: ')
              .form-control style="width: 40px"
                = f.check_box :active, { label: false }, true, false
        br
        .row
          .col-md-2
            = link_to_remove_association t('pioneer.shop.remove-store'), f