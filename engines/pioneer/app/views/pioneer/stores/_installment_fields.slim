.nested-fields
  .col-lg-12
    .panel.panel-default
      .panel-body
        .row
          .col-lg-2
            = f.label :tea, t('pioneer.stores.installments')
          .col-lg-2
            = f.number_field :number, class: 'form-control', step: :any, label: false
          .col-lg-2
        br
          .row
            .col-lg-2
              = f.label :tea, t('pioneer.stores.tea')
            .col-lg-2
              = f.number_field :tea, class: 'form-control', step: :any, label: false
            .col-lg-2
              = f.label :cft, t('pioneer.stores.cft')
            .col-lg-2
              = f.number_field :cft, class: 'form-control', step: :any, label: false
            .col-lg-2
              = f.label :coef, t('pioneer.stores.coef')
            .col-lg-2
              = f.number_field :coef, class: 'form-control', step: :any, label: false
          br
            .row
              .col-lg-2
                = f.label :cftna, t('pioneer.bines.cftna')
              .col-lg-2
                = f.text_field :cftna, placeholder: '', class: 'form-control', label: false
              .col-lg-2
                = f.label :tna, t('pioneer.bines.tna')
              .col-lg-2
                = f.text_field :tna, placeholder: '', class: 'form-control', label: false
              .col-lg-2
                = f.label :coef, t('pioneer.bines.coef')
              .col-lg-2
                = f.number_field :tna_coef, class: 'form-control', step: :any, label: false
        br
          .row
            .col-lg-2
              = link_to_remove_association t('pioneer.stores.installments-remove'), f