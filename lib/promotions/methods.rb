module Promotions::Methods
  def amount_by_installment(installment, promotion)
    (!percentage_fee_zero? && installment == 1) ? store.discount(amount) : (promotion.try(:total_amount, amount) || amount)
  end

  def percentage_fee_zero?
    store.percentage_fee.zero?
  end

  def build_installment(promotion)
    amount_installments = (promotion.try(:number) || promotion.installment).to_i
    price = amount_by_installment(amount_installments, promotion).round(2)
    {
      installments: amount_installments,
      base_amount: amount,
      amount: price,
      installments_amount: promotion.try(:total_by_installments, amount) || (price/amount_installments).round(2),
      cft: promotion.try(:cft)&.to_f || 0.0,
      tea: promotion.try(:tea)&.to_f || 0.0,
      tna: promotion.try(:tna)&.to_f || 0.0,
      coef: promotion.try(:coef)&.to_f || 0.0,
      category_id: promotion.try(:category_id),
      product_id: promotion.try(:product_id)
    }
  end

  def build_brand(bin)
    # NOTE: Skipped build_brand(MODO) when is modo and do not match with the bin associated to shop
    #return if bin.modo? && @shop.shop_stores.try(:first).try(:bin).try(:number) != bin.number
    return if bin.modo? && !@shop.shop_stores.any? { |s| s.try(:bin).try(:number) == bin.number }
    installments = installments_availables(bin)
    if installments.any?
      brand_name = bin.try(:brand) || bin.try(:first)&.brand || nil

      name = brand_name && I18n.t("promotions.#{brand_name}")
      if brand_name.present? && bin.modo?
        if @store.id == 20
          name = brand_name && I18n.t("promotions.#{brand_name}_ciudad")
        elsif @store.id == 41
          name = brand_name && I18n.t("promotions.#{brand_name}_bna")
        end
      end

      {
        brand: brand_name,
        name: name,
        installments: installments
      }
    else
      []
    end
  end
end
