module Promotions
  class Serializer
    include Methods
    attr_reader :amount, :store, :max_installments, :exclude_banks_ids

    def initialize(product, amount, max_installments, store)
      @product = product
      @amount = amount
      @store = store
      @max_installments = max_installments
      @exclude_banks_ids = []
      @shop = product.shop
      @payment_program = @shop.payment_program_for(store)
    end

    def build
      no_bines + bines + promotions
    end

    private

    def no_bines
      return [] if store.use_bines
      build_no_bines_installments(@payment_program ? @payment_program.installments : store.installments)
    end

    def build_no_bines_installments(installments)
      builded_installments = installments.order(:number).map { |installment| build_installment(installment) }
      [{
        name: 'Todos',
        bank_id: 1,
        brands: [{
          brand: 'Todas',
          name: 'Todas las tarjetas de credito',
          installments: builded_installments,
          max_installment_message: evaluate_installment_message(builded_installments)
        }]
      }]
    end

    def bines
      return [] unless store.use_bines
    
      if store.id == 20
        # Tomar bines del SHOP y si no hay, agregar los del STORE
        shop_bines = @shop.shop_stores.active_now.map(&:bin).compact
        store_bines = store.bines.unexpired.includes(:bank)
    
        all_bines = shop_bines.present? ? (shop_bines + store_bines).uniq : store_bines
    
        # Agrupar por banco + marca
        banks = all_bines.group_by { |bin| [bin.bank, bin.brand] }
    
        # Construcción de objetos para bancos
        result = banks.map do |(bank, _brand), bines|
          build_bank(bank, bines)
        end
    
        result.compact
      else
        # Para otros stores, mantener la lógica original
        shop_bines = @shop.shop_stores.map { |shop_store| shop_store.bin }.compact
        banks = store.bines.unexpired.includes(:bank).group(:bank_id, :brand).group_by { |b| b.bank }
        result = banks.map { |bank| build_bank(bank[0], bank[1]) }
        result.compact
      end
    end    

    def evaluate_installment_message(installments)
      one_installment?(installments) ? nil : max_installment_message(installments)
    end

    def max_installment_message(installments)
      installment = installments.last
      installment_text = installment[:installments] == 1 ? 'Cuota' : 'Cuotas'
      taxes_text = installment[:coef] == 0 ? 'sin interes' : 'Fijas'
      "Hasta #{installment[:installments]} #{installment_text} #{taxes_text}"\
        " de $#{installments.last[:installments_amount]}"
    end

    def one_installment?(installments)
      installments.last[:installments] == 1
    end

    def promotions
      Promotions::Calculator.new(payment_promotions, store, amount).build
    end

    def build_bank(bank, bines)
      @exclude_banks_ids << bank.id
      if bines.present? && bines.any? { |bin| build_brand(bin).to_a.any? }
        result = {
          name: bank.name,
          bank_id: bank.id,
          brands: bines.map{ |bin| build_brand(bin) }.compact
        }
        if @store.id == 41
          # NOTE: This append MODO installments when shop has the bin_id setted
          result[:brands] << build_brand(@shop.shop_stores.try(:first).try(:bin)) if @shop.shop_stores.try(:first).try(:bin)
        end

        result
      end
    end

    def installments_availables(bin)
      bin_installments = bin.available_installments_filtered_by(max_installments, @product)
      if !bin.debit_brand? && !bin.modo?
        installments = @payment_program ? (@payment_program.installments.order(:number)) : (bin_installments.sort_by(&:number))
      else
        installments = bin_installments.sort_by(&:number)
      end
      installments.map do |installment|
        build_installment(installment)
      end.compact
    end

    def payment_promotions
      return {} if store_gateway.nil?

      Mkp::PaymentPromotion.includes(:bank).where(gateway: store_gateway)
        .where.not(bank_id: exclude_banks_ids).group_by{ |b| b.bank }
    end

    def store_gateway
      return 'todopago' if store.gateway.include?('todopago')
      'mercadopago' if store.retrieve_gateways_array.include?('mercadopago')
    end
  end
end
