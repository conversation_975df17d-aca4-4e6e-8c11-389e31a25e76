require 'test_helper'

class Pages::Components::Cols4VariantsV1Test < ActiveSupport::TestCase
  def setup
    @store = create(:store)
    @landing = create(:landing, store: @store)
    @component = Pages::Components::Cols4VariantsV1.new(landing: @landing)
    
    @product1 = create(:product)
    @variant1 = create(:variant, product: @product1)
    
    # Simular configuración con solo 1 variante
    @component.setup = {
      items: [
        { variant_id: @variant1.id },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil }
      ]
    }
  end

  test "should not duplicate variants when only one is configured" do
    # Mock store para el componente
    @component.store = @store
    
    # Obtener las variantes destacadas
    variants = @component.featured_variants(@store)
    
    # Debe devolver solo 1 variante, no 8 duplicadas
    assert_equal 1, variants.length
    assert_equal @variant1.id, variants.first.id
  end

  test "should not duplicate variants when few are configured" do
    @product2 = create(:product)
    @variant2 = create(:variant, product: @product2)
    
    # Configurar 2 variantes
    @component.setup = {
      items: [
        { variant_id: @variant1.id },
        { variant_id: @variant2.id },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil },
        { variant_id: nil }
      ]
    }
    
    @component.store = @store
    variants = @component.featured_variants(@store)
    
    # Debe devolver solo 2 variantes, no duplicarlas hasta 8
    assert_equal 2, variants.length
    variant_ids = variants.map(&:id)
    assert_includes variant_ids, @variant1.id
    assert_includes variant_ids, @variant2.id
  end

  test "featured_variants_with_duplication should maintain old behavior" do
    @component.store = @store
    variants = @component.featured_variants_with_duplication(@store)
    
    # El método con duplicación debe completar hasta N_OF_ITEMS (8)
    assert_equal 8, variants.length
    
    # Todas deben ser la misma variante duplicada
    variants.each do |variant|
      assert_equal @variant1.id, variant.id
    end
  end
end
